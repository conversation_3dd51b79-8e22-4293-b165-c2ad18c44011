#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户数据分析脚本
分析每期SVIP用户、高意向用户、低意向用户的统计数据
"""

import pandas as pd
import numpy as np

def analyze_user_data(file_path):
    """
    分析用户数据
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        print("成功读取数据文件")
        print(f"数据形状: {df.shape}")
        print(f"列名: {df.columns.tolist()}")
        print("\n前5行数据:")
        print(df.head())
        
        # 检查必要的列是否存在
        required_columns = ['期数', '用户等级', '总金额']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"\n缺少必要的列: {missing_columns}")
            print("请确保Excel文件包含以下列: 期数, 用户等级, 总金额")
            return
        
        # 数据清洗
        df['总金额'] = pd.to_numeric(df['总金额'], errors='coerce').fillna(0)
        
        # 定义转化条件（总金额 > 500）
        df['是否转化'] = df['总金额'] > 500
        
        # 按期数和用户等级分组统计
        result_list = []
        
        for period in sorted(df['期数'].unique()):
            period_data = df[df['期数'] == period]
            
            for user_level in ['SVIP用户', '高意向用户', '低意向用户']:
                level_data = period_data[period_data['用户等级'] == user_level]
                
                if len(level_data) > 0:
                    total_users = len(level_data)
                    converted_users = len(level_data[level_data['是否转化']])
                    total_amount = level_data['总金额'].sum()
                    
                    # 计算转化率
                    conversion_rate = (converted_users / total_users * 100) if total_users > 0 else 0
                    
                    # 计算客单价（总金额/转化人数）
                    avg_order_value = (total_amount / converted_users) if converted_users > 0 else 0
                    
                    # 计算人均值（总金额/总人数）
                    avg_per_user = (total_amount / total_users) if total_users > 0 else 0
                    
                    result_list.append({
                        '期数': period,
                        '用户等级': user_level,
                        '总人数': total_users,
                        '转化人数': converted_users,
                        '总金额': total_amount,
                        '转化率(%)': round(conversion_rate, 2),
                        '客单价': round(avg_order_value, 2),
                        '人均值': round(avg_per_user, 2)
                    })
        
        # 创建结果DataFrame
        result_df = pd.DataFrame(result_list)
        
        # 显示结果
        print("\n=== 用户数据分析结果 ===")
        print(result_df.to_string(index=False))
        
        # 保存结果到Excel文件
        result_df.to_excel('用户分析结果.xlsx', index=False)
        print("\n结果已保存到 '用户分析结果.xlsx'")
        
        # 按期数汇总
        print("\n=== 按期数汇总 ===")
        period_summary = result_df.groupby('期数').agg({
            '总人数': 'sum',
            '转化人数': 'sum',
            '总金额': 'sum'
        }).reset_index()
        
        period_summary['总转化率(%)'] = round(
            period_summary['转化人数'] / period_summary['总人数'] * 100, 2
        )
        period_summary['总客单价'] = round(
            period_summary['总金额'] / period_summary['转化人数'], 2
        )
        period_summary['总人均值'] = round(
            period_summary['总金额'] / period_summary['总人数'], 2
        )
        
        print(period_summary.to_string(index=False))
        
        return result_df
        
    except Exception as e:
        print(f"分析过程中出现错误: {str(e)}")
        return None

if __name__ == "__main__":
    # 分析用户信息表
    result = analyze_user_data('用户信息表.xlsx')
