# 简化的结构化标签分析

Write-Host "=== 结构化标签转化率分析 ==="

# 创建Excel COM对象
$excel = New-Object -ComObject Excel.Application
$excel.Visible = $false
$excel.DisplayAlerts = $false

# 获取Excel文件
$excelFile = Get-ChildItem *.xlsx | Select-Object -First 1
$filePath = $excelFile.FullName
Write-Host "读取文件: $($excelFile.Name)"

# 打开工作簿
$workbook = $excel.Workbooks.Open($filePath)
$worksheet = $workbook.Worksheets.Item(1)

# 获取数据范围
$usedRange = $worksheet.UsedRange
$rowCount = $usedRange.Rows.Count

Write-Host "数据行数: $rowCount"

# 创建结果数组
$results = @()

# 分析第24期的工作阶段数据作为示例
Write-Host "`n分析第24期工作阶段数据..."

$workStageStats = @{}

for ($row = 2; $row -le $rowCount; $row++) {
    $period = $worksheet.Cells.Item($row, 1).Text
    $workStage = $worksheet.Cells.Item($row, 2).Text
    $amountText = $worksheet.Cells.Item($row, 14).Text
    
    if ($period -eq "第24期" -and $workStage -ne $null -and $workStage -ne '') {
        # 转换金额
        $amount = 0
        if ($amountText -ne $null -and $amountText -ne '' -and [double]::TryParse($amountText, [ref]$amount)) {
            # 金额已转换
        }
        
        if (-not $workStageStats.ContainsKey($workStage)) {
            $workStageStats[$workStage] = @{
                TotalUsers = 0
                ConvertedUsers = 0
                TotalAmount = 0
            }
        }
        
        $workStageStats[$workStage].TotalUsers++
        $workStageStats[$workStage].TotalAmount += $amount
        
        if ($amount -gt 500) {
            $workStageStats[$workStage].ConvertedUsers++
        }
    }
}

# 输出第24期工作阶段结果
Write-Host "`n第24期工作阶段分析结果:"
foreach ($stage in $workStageStats.Keys) {
    $stats = $workStageStats[$stage]
    $conversionRate = if ($stats.TotalUsers -gt 0) { [math]::Round(($stats.ConvertedUsers / $stats.TotalUsers) * 100, 2) } else { 0 }
    $avgOrderValue = if ($stats.ConvertedUsers -gt 0) { [math]::Round($stats.TotalAmount / $stats.ConvertedUsers, 2) } else { 0 }
    
    Write-Host "$stage : 总人数=$($stats.TotalUsers), 转化人数=$($stats.ConvertedUsers), 转化率=$conversionRate%, 总金额=$($stats.TotalAmount), 客单价=$avgOrderValue"
    
    $result = [PSCustomObject]@{
        Period = "第24期"
        TagCategory = "工作阶段"
        TagValue = $stage
        TotalUsers = $stats.TotalUsers
        ConvertedUsers = $stats.ConvertedUsers
        ConversionRate = $conversionRate
        TotalAmount = $stats.TotalAmount
        AvgOrderValue = $avgOrderValue
    }
    $results += $result
}

# 分析第24期的工作状态数据
Write-Host "`n分析第24期工作状态数据..."

$workStatusStats = @{}

for ($row = 2; $row -le $rowCount; $row++) {
    $period = $worksheet.Cells.Item($row, 1).Text
    $workStatus = $worksheet.Cells.Item($row, 3).Text
    $amountText = $worksheet.Cells.Item($row, 14).Text
    
    if ($period -eq "第24期" -and $workStatus -ne $null -and $workStatus -ne '') {
        # 转换金额
        $amount = 0
        if ($amountText -ne $null -and $amountText -ne '' -and [double]::TryParse($amountText, [ref]$amount)) {
            # 金额已转换
        }
        
        if (-not $workStatusStats.ContainsKey($workStatus)) {
            $workStatusStats[$workStatus] = @{
                TotalUsers = 0
                ConvertedUsers = 0
                TotalAmount = 0
            }
        }
        
        $workStatusStats[$workStatus].TotalUsers++
        $workStatusStats[$workStatus].TotalAmount += $amount
        
        if ($amount -gt 500) {
            $workStatusStats[$workStatus].ConvertedUsers++
        }
    }
}

# 输出第24期工作状态结果
Write-Host "`n第24期工作状态分析结果:"
foreach ($status in $workStatusStats.Keys) {
    $stats = $workStatusStats[$status]
    $conversionRate = if ($stats.TotalUsers -gt 0) { [math]::Round(($stats.ConvertedUsers / $stats.TotalUsers) * 100, 2) } else { 0 }
    $avgOrderValue = if ($stats.ConvertedUsers -gt 0) { [math]::Round($stats.TotalAmount / $stats.ConvertedUsers, 2) } else { 0 }
    
    Write-Host "$status : 总人数=$($stats.TotalUsers), 转化人数=$($stats.ConvertedUsers), 转化率=$conversionRate%, 总金额=$($stats.TotalAmount), 客单价=$avgOrderValue"
    
    $result = [PSCustomObject]@{
        Period = "第24期"
        TagCategory = "工作状态"
        TagValue = $status
        TotalUsers = $stats.TotalUsers
        ConvertedUsers = $stats.ConvertedUsers
        ConversionRate = $conversionRate
        TotalAmount = $stats.TotalAmount
        AvgOrderValue = $avgOrderValue
    }
    $results += $result
}

# 分析第24期的心力评分数据
Write-Host "`n分析第24期心力评分数据..."

$energyScoreStats = @{}

for ($row = 2; $row -le $rowCount; $row++) {
    $period = $worksheet.Cells.Item($row, 1).Text
    $energyScore = $worksheet.Cells.Item($row, 4).Text
    $amountText = $worksheet.Cells.Item($row, 14).Text
    
    if ($period -eq "第24期" -and $energyScore -ne $null -and $energyScore -ne '') {
        # 转换金额
        $amount = 0
        if ($amountText -ne $null -and $amountText -ne '' -and [double]::TryParse($amountText, [ref]$amount)) {
            # 金额已转换
        }
        
        if (-not $energyScoreStats.ContainsKey($energyScore)) {
            $energyScoreStats[$energyScore] = @{
                TotalUsers = 0
                ConvertedUsers = 0
                TotalAmount = 0
            }
        }
        
        $energyScoreStats[$energyScore].TotalUsers++
        $energyScoreStats[$energyScore].TotalAmount += $amount
        
        if ($amount -gt 500) {
            $energyScoreStats[$energyScore].ConvertedUsers++
        }
    }
}

# 输出第24期心力评分结果
Write-Host "`n第24期心力评分分析结果:"
foreach ($score in $energyScoreStats.Keys | Sort-Object) {
    $stats = $energyScoreStats[$score]
    $conversionRate = if ($stats.TotalUsers -gt 0) { [math]::Round(($stats.ConvertedUsers / $stats.TotalUsers) * 100, 2) } else { 0 }
    $avgOrderValue = if ($stats.ConvertedUsers -gt 0) { [math]::Round($stats.TotalAmount / $stats.ConvertedUsers, 2) } else { 0 }
    
    Write-Host "心力评分$score : 总人数=$($stats.TotalUsers), 转化人数=$($stats.ConvertedUsers), 转化率=$conversionRate%, 总金额=$($stats.TotalAmount), 客单价=$avgOrderValue"
    
    $result = [PSCustomObject]@{
        Period = "第24期"
        TagCategory = "心力评分"
        TagValue = $score
        TotalUsers = $stats.TotalUsers
        ConvertedUsers = $stats.ConvertedUsers
        ConversionRate = $conversionRate
        TotalAmount = $stats.TotalAmount
        AvgOrderValue = $avgOrderValue
    }
    $results += $result
}

# 关闭Excel
$workbook.Close($false)
$excel.Quit()
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null

# 保存结果
$results | Export-Csv -Path "第24期标签分析.csv" -NoTypeInformation -Encoding UTF8
Write-Host "`n结果已保存到 '第24期标签分析.csv'"

# 按转化率排序显示
Write-Host "`n=== 第24期转化率排序 ==="
$results | Sort-Object ConversionRate -Descending | Format-Table TagCategory, TagValue, TotalUsers, ConvertedUsers, ConversionRate, AvgOrderValue -AutoSize

Write-Host "`n=== 分析完成 ==="
