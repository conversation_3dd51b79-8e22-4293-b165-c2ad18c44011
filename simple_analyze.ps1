# 简化的PowerShell脚本：分析用户数据

# 检查并安装ImportExcel模块
Write-Host "检查ImportExcel模块..."
if (-not (Get-Module -ListAvailable -Name ImportExcel)) {
    Write-Host "正在安装ImportExcel模块..."
    try {
        Install-Module -Name ImportExcel -Force -Scope CurrentUser -AllowClobber
        Write-Host "ImportExcel模块安装成功"
    } catch {
        Write-Host "安装ImportExcel模块失败: $($_.Exception.Message)"
        Write-Host "尝试手动安装: Install-Module -Name ImportExcel -Force -Scope CurrentUser"
        exit
    }
}

# 导入模块
try {
    Import-Module ImportExcel
    Write-Host "ImportExcel模块导入成功"
} catch {
    Write-Host "导入ImportExcel模块失败: $($_.Exception.Message)"
    exit
}

# 读取Excel文件
$excelFile = "用户信息表.xlsx"

if (-not (Test-Path $excelFile)) {
    Write-Host "错误：找不到文件 $excelFile"
    exit
}

Write-Host "正在读取Excel文件: $excelFile"

try {
    # 读取Excel数据
    $data = Import-Excel -Path $excelFile
    
    Write-Host "成功读取数据，共 $($data.Count) 行"
    
    # 显示列名
    if ($data.Count -gt 0) {
        $columns = $data[0].PSObject.Properties.Name
        Write-Host "列名: $($columns -join ', ')"
        
        # 显示前5行数据
        Write-Host "`n前5行数据:"
        $data | Select-Object -First 5 | Format-Table
        
        # 分析数据
        Write-Host "`n=== 数据分析开始 ==="
        
        # 获取唯一的期数和用户等级
        $periods = $data | Select-Object -ExpandProperty '期数' -ErrorAction SilentlyContinue | Sort-Object -Unique
        $userLevels = $data | Select-Object -ExpandProperty '用户等级' -ErrorAction SilentlyContinue | Sort-Object -Unique
        
        Write-Host "发现的期数: $($periods -join ', ')"
        Write-Host "发现的用户等级: $($userLevels -join ', ')"
        
        # 创建结果表
        $results = @()
        
        foreach ($period in $periods) {
            if ($period -ne $null -and $period -ne '') {
                Write-Host "`n分析第 $period 期数据..."
                
                $periodData = $data | Where-Object { $_.'期数' -eq $period }
                
                foreach ($level in $userLevels) {
                    if ($level -ne $null -and $level -ne '') {
                        $levelData = $periodData | Where-Object { $_.'用户等级' -eq $level }
                        
                        if ($levelData.Count -gt 0) {
                            $totalUsers = $levelData.Count
                            $totalAmount = 0
                            $convertedUsers = 0
                            
                            # 计算总金额和转化人数
                            foreach ($user in $levelData) {
                                $amount = 0
                                if ($user.'总金额' -ne $null -and $user.'总金额' -ne '') {
                                    try {
                                        $amount = [double]$user.'总金额'
                                        $totalAmount += $amount
                                        if ($amount -gt 500) {
                                            $convertedUsers++
                                        }
                                    } catch {
                                        # 忽略无法转换的数据
                                    }
                                }
                            }
                            
                            # 计算指标
                            $conversionRate = if ($totalUsers -gt 0) { [math]::Round(($convertedUsers / $totalUsers) * 100, 2) } else { 0 }
                            $avgOrderValue = if ($convertedUsers -gt 0) { [math]::Round($totalAmount / $convertedUsers, 2) } else { 0 }
                            $avgPerUser = if ($totalUsers -gt 0) { [math]::Round($totalAmount / $totalUsers, 2) } else { 0 }
                            
                            $result = [PSCustomObject]@{
                                '期数' = $period
                                '用户等级' = $level
                                '总人数' = $totalUsers
                                '转化人数' = $convertedUsers
                                '总金额' = $totalAmount
                                '转化率(%)' = $conversionRate
                                '客单价' = $avgOrderValue
                                '人均值' = $avgPerUser
                            }
                            
                            $results += $result
                            
                            Write-Host "  $level : 总人数=$totalUsers, 转化人数=$convertedUsers, 总金额=$totalAmount, 转化率=$conversionRate%"
                        }
                    }
                }
            }
        }
        
        # 显示完整结果
        Write-Host "`n=== 完整分析结果 ==="
        $results | Format-Table -AutoSize
        
        # 保存结果
        try {
            $results | Export-Excel -Path "用户分析结果.xlsx" -AutoSize -TableStyle Medium2
            Write-Host "`n结果已保存到 '用户分析结果.xlsx'"
        } catch {
            Write-Host "保存Excel文件失败，尝试保存为CSV格式..."
            $results | Export-Csv -Path "用户分析结果.csv" -NoTypeInformation -Encoding UTF8
            Write-Host "结果已保存到 '用户分析结果.csv'"
        }
        
    } else {
        Write-Host "Excel文件为空或无法读取数据"
    }
    
} catch {
    Write-Host "处理Excel文件时出错: $($_.Exception.Message)"
    Write-Host "错误详情: $($_.Exception.ToString())"
}
