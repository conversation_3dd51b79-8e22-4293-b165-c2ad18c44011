# Use COM object to read Excel file

Write-Host "Attempting to read Excel file using COM object..."

try {
    # Create Excel COM object
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    
    # Get the Excel file path
    $excelFile = Get-ChildItem *.xlsx | Select-Object -First 1
    $filePath = $excelFile.FullName
    Write-Host "Opening file: $filePath"

    # Open workbook
    $workbook = $excel.Workbooks.Open($filePath)
    $worksheet = $workbook.Worksheets.Item(1)
    
    # Get used range
    $usedRange = $worksheet.UsedRange
    $rowCount = $usedRange.Rows.Count
    $colCount = $usedRange.Columns.Count
    
    Write-Host "Excel file opened successfully"
    Write-Host "Rows: $rowCount, Columns: $colCount"
    
    # Read header row
    Write-Host "`nColumn headers:"
    for ($col = 1; $col -le $colCount; $col++) {
        $header = $worksheet.Cells.Item(1, $col).Text
        Write-Host "Column $col : $header"
    }
    
    # Read first few data rows
    Write-Host "`nFirst 5 data rows:"
    for ($row = 2; $row -le [Math]::Min(6, $rowCount); $row++) {
        Write-Host "Row $row :"
        for ($col = 1; $col -le $colCount; $col++) {
            $value = $worksheet.Cells.Item($row, $col).Text
            Write-Host "  Col$col : $value"
        }
    }
    
    # Close and cleanup
    $workbook.Close($false)
    $excel.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    
    Write-Host "`nExcel file read successfully using COM object"
    
} catch {
    Write-Host "Error reading Excel file: $($_.Exception.Message)"
    
    # Cleanup in case of error
    if ($workbook) { $workbook.Close($false) }
    if ($excel) { 
        $excel.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    }
}
