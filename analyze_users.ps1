# PowerShell脚本：分析用户数据
# 需要安装ImportExcel模块

# 检查并安装ImportExcel模块
if (-not (Get-Module -ListAvailable -Name ImportExcel)) {
    Write-Host "正在安装ImportExcel模块..."
    Install-Module -Name ImportExcel -Force -Scope CurrentUser
}

# 导入模块
Import-Module ImportExcel

# 读取Excel文件
$excelFile = "用户信息表.xlsx"

if (-not (Test-Path $excelFile)) {
    Write-Host "错误：找不到文件 $excelFile"
    exit
}

Write-Host "正在读取Excel文件: $excelFile"

try {
    # 读取Excel数据
    $data = Import-Excel -Path $excelFile
    
    Write-Host "成功读取数据，共 $($data.Count) 行"
    
    # 显示列名
    $columns = $data[0].PSObject.Properties.Name
    Write-Host "列名: $($columns -join ', ')"
    
    # 显示前5行数据
    Write-Host "`n前5行数据:"
    $data | Select-Object -First 5 | Format-Table
    
    # 检查必要的列
    $requiredColumns = @('期数', '用户等级', '总金额')
    $missingColumns = @()
    
    foreach ($col in $requiredColumns) {
        if ($col -notin $columns) {
            $missingColumns += $col
        }
    }
    
    if ($missingColumns.Count -gt 0) {
        Write-Host "错误：缺少必要的列: $($missingColumns -join ', ')"
        Write-Host "请确保Excel文件包含以下列: 期数, 用户等级, 总金额"
        exit
    }
    
    # 数据分析
    Write-Host "`n=== 开始数据分析 ==="
    
    # 创建结果数组
    $results = @()
    
    # 获取所有期数
    $periods = $data | Select-Object -ExpandProperty '期数' | Sort-Object -Unique
    
    foreach ($period in $periods) {
        $periodData = $data | Where-Object { $_.'期数' -eq $period }
        
        # 分析每个用户等级
        $userLevels = @('SVIP用户', '高意向用户', '低意向用户')
        
        foreach ($level in $userLevels) {
            $levelData = $periodData | Where-Object { $_.'用户等级' -eq $level }
            
            if ($levelData.Count -gt 0) {
                # 转换总金额为数值
                $amounts = @()
                foreach ($item in $levelData) {
                    $amount = 0
                    if ($item.'总金额' -ne $null -and $item.'总金额' -ne '') {
                        try {
                            $amount = [double]$item.'总金额'
                        } catch {
                            $amount = 0
                        }
                    }
                    $amounts += $amount
                }
                
                $totalUsers = $levelData.Count
                $convertedUsers = ($amounts | Where-Object { $_ -gt 500 }).Count
                $totalAmount = ($amounts | Measure-Object -Sum).Sum
                
                # 计算指标
                $conversionRate = if ($totalUsers -gt 0) { [math]::Round(($convertedUsers / $totalUsers) * 100, 2) } else { 0 }
                $avgOrderValue = if ($convertedUsers -gt 0) { [math]::Round($totalAmount / $convertedUsers, 2) } else { 0 }
                $avgPerUser = if ($totalUsers -gt 0) { [math]::Round($totalAmount / $totalUsers, 2) } else { 0 }
                
                $results += [PSCustomObject]@{
                    '期数' = $period
                    '用户等级' = $level
                    '总人数' = $totalUsers
                    '转化人数' = $convertedUsers
                    '总金额' = $totalAmount
                    '转化率(%)' = $conversionRate
                    '客单价' = $avgOrderValue
                    '人均值' = $avgPerUser
                }
            }
        }
    }
    
    # 显示结果
    Write-Host "`n=== 用户数据分析结果 ==="
    $results | Format-Table -AutoSize
    
    # 导出结果到Excel
    $results | Export-Excel -Path "用户分析结果.xlsx" -AutoSize -TableStyle Medium2
    Write-Host "`n结果已保存到 '用户分析结果.xlsx'"
    
    # 按期数汇总
    Write-Host "`n=== 按期数汇总 ==="
    $periodSummary = $results | Group-Object '期数' | ForEach-Object {
        $periodData = $_.Group
        $totalUsers = ($periodData | Measure-Object '总人数' -Sum).Sum
        $totalConverted = ($periodData | Measure-Object '转化人数' -Sum).Sum
        $totalAmount = ($periodData | Measure-Object '总金额' -Sum).Sum

        [PSCustomObject]@{
            '期数' = $_.Name
            '总人数' = $totalUsers
            '转化人数' = $totalConverted
            '总金额' = $totalAmount
            '总转化率(%)' = if ($totalUsers -gt 0) { [math]::Round(($totalConverted / $totalUsers) * 100, 2) } else { 0 }
            '总客单价' = if ($totalConverted -gt 0) { [math]::Round($totalAmount / $totalConverted, 2) } else { 0 }
            '总人均值' = if ($totalUsers -gt 0) { [math]::Round($totalAmount / $totalUsers, 2) } else { 0 }
        }
    }

    $periodSummary | Format-Table -AutoSize
    
} catch {
    Write-Host "错误：$($_.Exception.Message)"
}
