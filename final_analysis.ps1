# 完整的用户数据分析脚本

Write-Host "=== 用户数据分析开始 ==="

try {
    # 创建Excel COM对象
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    
    # 获取Excel文件路径
    $excelFile = Get-ChildItem *.xlsx | Select-Object -First 1
    $filePath = $excelFile.FullName
    Write-Host "正在读取文件: $($excelFile.Name)"
    
    # 打开工作簿
    $workbook = $excel.Workbooks.Open($filePath)
    $worksheet = $workbook.Worksheets.Item(1)
    
    # 获取使用范围
    $usedRange = $worksheet.UsedRange
    $rowCount = $usedRange.Rows.Count
    $colCount = $usedRange.Columns.Count
    
    Write-Host "数据行数: $rowCount, 列数: $colCount"
    
    # 读取所有数据到数组
    $data = @()
    
    # 读取表头
    $headers = @()
    for ($col = 1; $col -le $colCount; $col++) {
        $headers += $worksheet.Cells.Item(1, $col).Text
    }
    
    Write-Host "列名: $($headers -join ', ')"
    
    # 找到关键列的索引
    $periodCol = -1
    $userLevelCol = -1
    $amountCol = -1
    
    for ($i = 0; $i -lt $headers.Count; $i++) {
        if ($headers[$i] -like "*期数*") { $periodCol = $i + 1 }
        if ($headers[$i] -like "*AI筛选*") { $userLevelCol = $i + 1 }
        if ($headers[$i] -like "*总金额*") { $amountCol = $i + 1 }
    }
    
    Write-Host "期数列: $periodCol, 用户等级列: $userLevelCol, 总金额列: $amountCol"
    
    if ($periodCol -eq -1 -or $userLevelCol -eq -1 -or $amountCol -eq -1) {
        Write-Host "错误: 无法找到必要的列"
        return
    }
    
    # 读取数据行
    for ($row = 2; $row -le $rowCount; $row++) {
        $period = $worksheet.Cells.Item($row, $periodCol).Text
        $userLevel = $worksheet.Cells.Item($row, $userLevelCol).Text
        $amountText = $worksheet.Cells.Item($row, $amountCol).Text
        
        # 转换金额为数字
        $amount = 0
        if ($amountText -ne $null -and $amountText -ne '') {
            try {
                $amount = [double]$amountText
            } catch {
                $amount = 0
            }
        }
        
        $data += [PSCustomObject]@{
            Period = $period
            UserLevel = $userLevel
            Amount = $amount
        }
    }
    
    # 关闭Excel
    $workbook.Close($false)
    $excel.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    
    Write-Host "数据读取完成，共 $($data.Count) 条记录"
    
    # 数据分析
    Write-Host "`n=== 开始数据分析 ==="
    
    # 获取唯一值
    $periods = $data | Where-Object { $_.Period -ne $null -and $_.Period -ne '' } | Select-Object -ExpandProperty Period | Sort-Object -Unique
    $userLevels = $data | Where-Object { $_.UserLevel -ne $null -and $_.UserLevel -ne '' } | Select-Object -ExpandProperty UserLevel | Sort-Object -Unique
    
    Write-Host "发现的期数: $($periods -join ', ')"
    Write-Host "发现的用户等级: $($userLevels -join ', ')"
    
    # 创建结果数组
    $results = @()
    
    # 分析每个期数的每个用户等级
    foreach ($period in $periods) {
        Write-Host "`n--- 分析 $period ---"
        
        $periodData = $data | Where-Object { $_.Period -eq $period }
        
        foreach ($level in $userLevels) {
            $levelData = $periodData | Where-Object { $_.UserLevel -eq $level }
            
            if ($levelData.Count -gt 0) {
                $totalUsers = $levelData.Count
                $totalAmount = ($levelData | Measure-Object Amount -Sum).Sum
                $convertedUsers = ($levelData | Where-Object { $_.Amount -gt 500 }).Count
                
                # 计算指标
                $conversionRate = 0
                if ($totalUsers -gt 0) {
                    $conversionRate = [math]::Round(($convertedUsers / $totalUsers) * 100, 2)
                }
                
                $avgOrderValue = 0
                if ($convertedUsers -gt 0) {
                    $avgOrderValue = [math]::Round($totalAmount / $convertedUsers, 2)
                }
                
                $avgPerUser = 0
                if ($totalUsers -gt 0) {
                    $avgPerUser = [math]::Round($totalAmount / $totalUsers, 2)
                }
                
                $result = [PSCustomObject]@{
                    '期数' = $period
                    '用户等级' = $level
                    '总人数' = $totalUsers
                    '转化人数' = $convertedUsers
                    '总金额' = $totalAmount
                    '转化率(%)' = $conversionRate
                    '客单价' = $avgOrderValue
                    '人均值' = $avgPerUser
                }
                
                $results += $result
                
                Write-Host "$level : 总人数=$totalUsers, 转化人数=$convertedUsers, 总金额=$totalAmount, 转化率=$conversionRate%"
            }
        }
    }
    
    # 显示完整结果
    Write-Host "`n=== 完整分析结果 ==="
    $results | Format-Table -AutoSize
    
    # 保存结果到CSV
    $results | Export-Csv -Path "用户分析结果.csv" -NoTypeInformation -Encoding UTF8
    Write-Host "`n结果已保存到 '用户分析结果.csv'"
    
    # 按期数汇总
    Write-Host "`n=== 按期数汇总 ==="
    $periodSummary = @()
    
    foreach ($period in $periods) {
        $periodResults = $results | Where-Object { $_.'期数' -eq $period }
        
        $totalUsers = ($periodResults | Measure-Object '总人数' -Sum).Sum
        $totalConverted = ($periodResults | Measure-Object '转化人数' -Sum).Sum
        $totalAmount = ($periodResults | Measure-Object '总金额' -Sum).Sum
        
        $totalConversionRate = 0
        if ($totalUsers -gt 0) {
            $totalConversionRate = [math]::Round(($totalConverted / $totalUsers) * 100, 2)
        }
        
        $totalAvgOrderValue = 0
        if ($totalConverted -gt 0) {
            $totalAvgOrderValue = [math]::Round($totalAmount / $totalConverted, 2)
        }
        
        $totalAvgPerUser = 0
        if ($totalUsers -gt 0) {
            $totalAvgPerUser = [math]::Round($totalAmount / $totalUsers, 2)
        }
        
        $summary = [PSCustomObject]@{
            '期数' = $period
            '总人数' = $totalUsers
            '转化人数' = $totalConverted
            '总金额' = $totalAmount
            '转化率(%)' = $totalConversionRate
            '客单价' = $totalAvgOrderValue
            '人均值' = $totalAvgPerUser
        }
        
        $periodSummary += $summary
    }
    
    $periodSummary | Format-Table -AutoSize
    
    # 保存汇总结果
    $periodSummary | Export-Csv -Path "期数汇总结果.csv" -NoTypeInformation -Encoding UTF8
    Write-Host "期数汇总结果已保存到 '期数汇总结果.csv'"
    
} catch {
    Write-Host "Analysis error: $($_.Exception.Message)"

    # Cleanup COM objects
    if ($workbook) { $workbook.Close($false) }
    if ($excel) {
        $excel.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    }
}

Write-Host "`n=== Analysis Complete ==="
