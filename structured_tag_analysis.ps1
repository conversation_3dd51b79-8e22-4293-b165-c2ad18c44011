# 结构化标签转化率分析脚本

Write-Host "=== 结构化标签转化率分析 ==="

try {
    # 创建Excel COM对象
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false
    
    # 获取Excel文件
    $excelFile = Get-ChildItem *.xlsx | Select-Object -First 1
    $filePath = $excelFile.FullName
    Write-Host "读取文件: $($excelFile.Name)"
    
    # 打开工作簿
    $workbook = $excel.Workbooks.Open($filePath)
    $worksheet = $workbook.Worksheets.Item(1)
    
    # 获取数据范围
    $usedRange = $worksheet.UsedRange
    $rowCount = $usedRange.Rows.Count
    $colCount = $usedRange.Columns.Count
    
    Write-Host "数据行数: $rowCount, 列数: $colCount"
    
    # 读取表头
    $headers = @()
    for ($col = 1; $col -le $colCount; $col++) {
        $headers += $worksheet.Cells.Item(1, $col).Text
    }
    
    Write-Host "表头: $($headers -join ' | ')"
    
    # 定义要分析的列索引（基于观察到的数据结构）
    $periodCol = 1      # 期数
    $workStageCol = 2   # 工作阶段
    $workStatusCol = 3  # 工作状态
    $energyScoreCol = 4 # 心力评分
    $workYearsCol = 8   # 工作年限
    $salaryCol = 9      # 当前薪资区间
    $investmentCol = 10 # 投入成本
    $cityTypeCol = 7    # 城市类别
    $amountCol = 14     # 总金额
    
    # 读取所有数据
    $allData = @()
    for ($row = 2; $row -le $rowCount; $row++) {
        $period = $worksheet.Cells.Item($row, $periodCol).Text
        $workStage = $worksheet.Cells.Item($row, $workStageCol).Text
        $workStatus = $worksheet.Cells.Item($row, $workStatusCol).Text
        $energyScore = $worksheet.Cells.Item($row, $energyScoreCol).Text
        $workYears = $worksheet.Cells.Item($row, $workYearsCol).Text
        $salary = $worksheet.Cells.Item($row, $salaryCol).Text
        $investment = $worksheet.Cells.Item($row, $investmentCol).Text
        $cityType = $worksheet.Cells.Item($row, $cityTypeCol).Text
        $amountText = $worksheet.Cells.Item($row, $amountCol).Text
        
        # 转换金额
        $amount = 0
        if ($amountText -ne $null -and $amountText -ne '' -and [double]::TryParse($amountText, [ref]$amount)) {
            # 金额已转换
        } else {
            $amount = 0
        }
        
        $dataRow = [PSCustomObject]@{
            Period = $period
            WorkStage = $workStage
            WorkStatus = $workStatus
            EnergyScore = $energyScore
            WorkYears = $workYears
            Salary = $salary
            Investment = $investment
            CityType = $cityType
            Amount = $amount
            IsConverted = ($amount -gt 500)
        }
        
        $allData += $dataRow
    }
    
    # 关闭Excel
    $workbook.Close($false)
    $excel.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    
    Write-Host "数据读取完成，共 $($allData.Count) 条记录"
    
    # 获取所有期数
    $periods = $allData | Where-Object { $_.Period -ne $null -and $_.Period -ne '' } | Select-Object -ExpandProperty Period | Sort-Object -Unique
    Write-Host "发现期数: $($periods -join ', ')"
    
    Write-Host "数据读取完成，共 $($allData.Count) 条记录"

    # 获取所有期数
    $periods = $allData | Where-Object { $_.Period -ne $null -and $_.Period -ne '' } | Select-Object -ExpandProperty Period | Sort-Object -Unique
    Write-Host "发现期数: $($periods -join ', ')"
    
    # 分析所有标签
    $allResults = @()
    
    foreach ($period in $periods) {
        Write-Host "`n分析 $period ..."
        
        # 工作阶段
        $workStageResults = Analyze-TagConversion -data $allData -tagProperty "WorkStage" -tagName "工作阶段" -period $period
        $allResults += $workStageResults
        
        # 工作状态
        $workStatusResults = Analyze-TagConversion -data $allData -tagProperty "WorkStatus" -tagName "工作状态" -period $period
        $allResults += $workStatusResults
        
        # 心力评分
        $energyScoreResults = Analyze-TagConversion -data $allData -tagProperty "EnergyScore" -tagName "心力评分" -period $period
        $allResults += $energyScoreResults
        
        # 工作年限
        $workYearsResults = Analyze-TagConversion -data $allData -tagProperty "WorkYears" -tagName "工作年限" -period $period
        $allResults += $workYearsResults
        
        # 薪资区间
        $salaryResults = Analyze-TagConversion -data $allData -tagProperty "Salary" -tagName "薪资区间" -period $period
        $allResults += $salaryResults
        
        # 投入成本
        $investmentResults = Analyze-TagConversion -data $allData -tagProperty "Investment" -tagName "投入成本" -period $period
        $allResults += $investmentResults
        
        # 城市类别
        $cityTypeResults = Analyze-TagConversion -data $allData -tagProperty "CityType" -tagName "城市类别" -period $period
        $allResults += $cityTypeResults
    }
    
    # 保存详细结果
    $allResults | Export-Csv -Path "结构化标签转化分析.csv" -NoTypeInformation -Encoding UTF8
    Write-Host "`n详细结果已保存到 '结构化标签转化分析.csv'"
    
    # 按转化率排序显示前20名
    Write-Host "`n=== 转化率TOP20 ==="
    $topConversions = $allResults | Where-Object { $_.TotalUsers -ge 5 } | Sort-Object ConversionRate -Descending | Select-Object -First 20
    $topConversions | Format-Table Period, TagCategory, TagValue, TotalUsers, ConvertedUsers, ConversionRate, AvgOrderValue -AutoSize
    
    # 按标签类别汇总
    Write-Host "`n=== 按标签类别汇总 ==="
    $categoryStats = $allResults | Group-Object TagCategory | ForEach-Object {
        $categoryData = $_.Group
        $totalUsers = ($categoryData | Measure-Object TotalUsers -Sum).Sum
        $totalConverted = ($categoryData | Measure-Object ConvertedUsers -Sum).Sum
        $totalAmount = ($categoryData | Measure-Object TotalAmount -Sum).Sum
        
        [PSCustomObject]@{
            TagCategory = $_.Name
            TotalUsers = $totalUsers
            ConvertedUsers = $totalConverted
            TotalAmount = $totalAmount
            ConversionRate = if ($totalUsers -gt 0) { [math]::Round(($totalConverted / $totalUsers) * 100, 2) } else { 0 }
            AvgOrderValue = if ($totalConverted -gt 0) { [math]::Round($totalAmount / $totalConverted, 2) } else { 0 }
        }
    }
    
    $categoryStats | Sort-Object ConversionRate -Descending | Format-Table -AutoSize
    
    # 保存汇总结果
    $categoryStats | Export-Csv -Path "标签类别汇总.csv" -NoTypeInformation -Encoding UTF8
    Write-Host "标签类别汇总已保存到 '标签类别汇总.csv'"
    
} catch {
    Write-Host "分析过程中出现错误: $($_.Exception.Message)"
    
    # 清理COM对象
    if ($workbook) { $workbook.Close($false) }
    if ($excel) { 
        $excel.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    }
}

Write-Host "`n=== 分析完成 ==="
