# Structured Tag Conversion Analysis

Write-Host "=== Structured Tag Conversion Analysis ==="

# Create Excel COM object
$excel = New-Object -ComObject Excel.Application
$excel.Visible = $false
$excel.DisplayAlerts = $false

# Get Excel file
$excelFile = Get-ChildItem *.xlsx | Select-Object -First 1
$filePath = $excelFile.FullName
Write-Host "Reading file: $($excelFile.Name)"

# Open workbook
$workbook = $excel.Workbooks.Open($filePath)
$worksheet = $workbook.Worksheets.Item(1)

# Get data range
$usedRange = $worksheet.UsedRange
$rowCount = $usedRange.Rows.Count

Write-Host "Total rows: $rowCount"

# Create results array
$results = @()

# Analyze Period 24 Work Stage data
Write-Host "`nAnalyzing Period 24 Work Stage data..."

$workStageStats = @{}

for ($row = 2; $row -le $rowCount; $row++) {
    $period = $worksheet.Cells.Item($row, 1).Text
    $workStage = $worksheet.Cells.Item($row, 2).Text
    $amountText = $worksheet.Cells.Item($row, 14).Text
    
    if ($period -eq "第24期" -and $workStage -ne $null -and $workStage -ne '') {
        # Convert amount
        $amount = 0
        if ($amountText -ne $null -and $amountText -ne '' -and [double]::TryParse($amountText, [ref]$amount)) {
            # Amount converted
        }
        
        if (-not $workStageStats.ContainsKey($workStage)) {
            $workStageStats[$workStage] = @{
                TotalUsers = 0
                ConvertedUsers = 0
                TotalAmount = 0
            }
        }
        
        $workStageStats[$workStage].TotalUsers++
        $workStageStats[$workStage].TotalAmount += $amount
        
        if ($amount -gt 500) {
            $workStageStats[$workStage].ConvertedUsers++
        }
    }
}

# Output Period 24 Work Stage results
Write-Host "`nPeriod 24 Work Stage Analysis Results:"
foreach ($stage in $workStageStats.Keys) {
    $stats = $workStageStats[$stage]
    $conversionRate = if ($stats.TotalUsers -gt 0) { [math]::Round(($stats.ConvertedUsers / $stats.TotalUsers) * 100, 2) } else { 0 }
    $avgOrderValue = if ($stats.ConvertedUsers -gt 0) { [math]::Round($stats.TotalAmount / $stats.ConvertedUsers, 2) } else { 0 }
    
    Write-Host "$stage : Total=$($stats.TotalUsers), Converted=$($stats.ConvertedUsers), Rate=$conversionRate%, Amount=$($stats.TotalAmount), AOV=$avgOrderValue"
    
    $result = [PSCustomObject]@{
        Period = "Period24"
        TagCategory = "WorkStage"
        TagValue = $stage
        TotalUsers = $stats.TotalUsers
        ConvertedUsers = $stats.ConvertedUsers
        ConversionRate = $conversionRate
        TotalAmount = $stats.TotalAmount
        AvgOrderValue = $avgOrderValue
    }
    $results += $result
}

# Analyze Period 24 Work Status data
Write-Host "`nAnalyzing Period 24 Work Status data..."

$workStatusStats = @{}

for ($row = 2; $row -le $rowCount; $row++) {
    $period = $worksheet.Cells.Item($row, 1).Text
    $workStatus = $worksheet.Cells.Item($row, 3).Text
    $amountText = $worksheet.Cells.Item($row, 14).Text
    
    if ($period -eq "第24期" -and $workStatus -ne $null -and $workStatus -ne '') {
        # Convert amount
        $amount = 0
        if ($amountText -ne $null -and $amountText -ne '' -and [double]::TryParse($amountText, [ref]$amount)) {
            # Amount converted
        }
        
        if (-not $workStatusStats.ContainsKey($workStatus)) {
            $workStatusStats[$workStatus] = @{
                TotalUsers = 0
                ConvertedUsers = 0
                TotalAmount = 0
            }
        }
        
        $workStatusStats[$workStatus].TotalUsers++
        $workStatusStats[$workStatus].TotalAmount += $amount
        
        if ($amount -gt 500) {
            $workStatusStats[$workStatus].ConvertedUsers++
        }
    }
}

# Output Period 24 Work Status results
Write-Host "`nPeriod 24 Work Status Analysis Results:"
foreach ($status in $workStatusStats.Keys) {
    $stats = $workStatusStats[$status]
    $conversionRate = if ($stats.TotalUsers -gt 0) { [math]::Round(($stats.ConvertedUsers / $stats.TotalUsers) * 100, 2) } else { 0 }
    $avgOrderValue = if ($stats.ConvertedUsers -gt 0) { [math]::Round($stats.TotalAmount / $stats.ConvertedUsers, 2) } else { 0 }
    
    Write-Host "$status : Total=$($stats.TotalUsers), Converted=$($stats.ConvertedUsers), Rate=$conversionRate%, Amount=$($stats.TotalAmount), AOV=$avgOrderValue"
    
    $result = [PSCustomObject]@{
        Period = "Period24"
        TagCategory = "WorkStatus"
        TagValue = $status
        TotalUsers = $stats.TotalUsers
        ConvertedUsers = $stats.ConvertedUsers
        ConversionRate = $conversionRate
        TotalAmount = $stats.TotalAmount
        AvgOrderValue = $avgOrderValue
    }
    $results += $result
}

# Analyze Period 24 Energy Score data
Write-Host "`nAnalyzing Period 24 Energy Score data..."

$energyScoreStats = @{}

for ($row = 2; $row -le $rowCount; $row++) {
    $period = $worksheet.Cells.Item($row, 1).Text
    $energyScore = $worksheet.Cells.Item($row, 4).Text
    $amountText = $worksheet.Cells.Item($row, 14).Text
    
    if ($period -eq "第24期" -and $energyScore -ne $null -and $energyScore -ne '') {
        # Convert amount
        $amount = 0
        if ($amountText -ne $null -and $amountText -ne '' -and [double]::TryParse($amountText, [ref]$amount)) {
            # Amount converted
        }
        
        if (-not $energyScoreStats.ContainsKey($energyScore)) {
            $energyScoreStats[$energyScore] = @{
                TotalUsers = 0
                ConvertedUsers = 0
                TotalAmount = 0
            }
        }
        
        $energyScoreStats[$energyScore].TotalUsers++
        $energyScoreStats[$energyScore].TotalAmount += $amount
        
        if ($amount -gt 500) {
            $energyScoreStats[$energyScore].ConvertedUsers++
        }
    }
}

# Output Period 24 Energy Score results
Write-Host "`nPeriod 24 Energy Score Analysis Results:"
foreach ($score in $energyScoreStats.Keys | Sort-Object) {
    $stats = $energyScoreStats[$score]
    $conversionRate = if ($stats.TotalUsers -gt 0) { [math]::Round(($stats.ConvertedUsers / $stats.TotalUsers) * 100, 2) } else { 0 }
    $avgOrderValue = if ($stats.ConvertedUsers -gt 0) { [math]::Round($stats.TotalAmount / $stats.ConvertedUsers, 2) } else { 0 }
    
    Write-Host "Score$score : Total=$($stats.TotalUsers), Converted=$($stats.ConvertedUsers), Rate=$conversionRate%, Amount=$($stats.TotalAmount), AOV=$avgOrderValue"
    
    $result = [PSCustomObject]@{
        Period = "Period24"
        TagCategory = "EnergyScore"
        TagValue = $score
        TotalUsers = $stats.TotalUsers
        ConvertedUsers = $stats.ConvertedUsers
        ConversionRate = $conversionRate
        TotalAmount = $stats.TotalAmount
        AvgOrderValue = $avgOrderValue
    }
    $results += $result
}

# Close Excel
$workbook.Close($false)
$excel.Quit()
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null

# Save results
$results | Export-Csv -Path "Period24_Tag_Analysis.csv" -NoTypeInformation -Encoding UTF8
Write-Host "`nResults saved to 'Period24_Tag_Analysis.csv'"

# Display results sorted by conversion rate
Write-Host "`n=== Period 24 Conversion Rate Ranking ==="
$results | Sort-Object ConversionRate -Descending | Format-Table TagCategory, TagValue, TotalUsers, ConvertedUsers, ConversionRate, AvgOrderValue -AutoSize

Write-Host "`n=== Analysis Complete ==="
