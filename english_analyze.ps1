# PowerShell script for user data analysis (English version)

Write-Host "Starting user data analysis..."

# Check and install ImportExcel module
$moduleInstalled = Get-Module -ListAvailable -Name ImportExcel
if (-not $moduleInstalled) {
    Write-Host "Installing ImportExcel module..."
    Install-Module -Name ImportExcel -Force -Scope CurrentUser -AllowClobber
}

# Import module
Import-Module ImportExcel

# Check if file exists
$excelFile = "用户信息表.xlsx"
if (-not (Test-Path $excelFile)) {
    Write-Host "Error: Cannot find file $excelFile"
    Read-Host "Press any key to exit"
    exit
}

Write-Host "Reading Excel file: $excelFile"

# Read Excel data
$data = Import-Excel -Path $excelFile

Write-Host "Successfully read data, total $($data.Count) rows"

# Display column names
if ($data.Count -gt 0) {
    $columns = $data[0].PSObject.Properties.Name
    Write-Host "Columns: $($columns -join ', ')"
    
    # Display first 3 rows as example
    Write-Host "`nFirst 3 rows example:"
    $data | Select-Object -First 3 | Format-Table
}

# Get unique values
$periods = $data | Select-Object -ExpandProperty '期数' | Where-Object { $_ -ne $null -and $_ -ne '' } | Sort-Object -Unique
$userLevels = $data | Select-Object -ExpandProperty '用户等级' | Where-Object { $_ -ne $null -and $_ -ne '' } | Sort-Object -Unique

Write-Host "`nFound periods: $($periods -join ', ')"
Write-Host "Found user levels: $($userLevels -join ', ')"

# Create results array
$results = @()

Write-Host "`nStarting detailed analysis..."

# Analyze each period and user level
foreach ($period in $periods) {
    Write-Host "`n--- Analyzing Period $period ---"
    
    $periodData = $data | Where-Object { $_.'期数' -eq $period }
    
    foreach ($level in $userLevels) {
        $levelData = $periodData | Where-Object { $_.'用户等级' -eq $level }
        
        if ($levelData.Count -gt 0) {
            $totalUsers = $levelData.Count
            $totalAmount = 0
            $convertedUsers = 0
            
            # Process each user data
            foreach ($user in $levelData) {
                $amount = 0
                if ($user.'总金额' -ne $null -and $user.'总金额' -ne '') {
                    # Try to convert to number
                    if ([double]::TryParse($user.'总金额', [ref]$amount)) {
                        $totalAmount += $amount
                        if ($amount -gt 500) {
                            $convertedUsers++
                        }
                    }
                }
            }
            
            # Calculate metrics
            $conversionRate = 0
            if ($totalUsers -gt 0) {
                $conversionRate = [math]::Round(($convertedUsers / $totalUsers) * 100, 2)
            }
            
            $avgOrderValue = 0
            if ($convertedUsers -gt 0) {
                $avgOrderValue = [math]::Round($totalAmount / $convertedUsers, 2)
            }
            
            $avgPerUser = 0
            if ($totalUsers -gt 0) {
                $avgPerUser = [math]::Round($totalAmount / $totalUsers, 2)
            }
            
            # Create result object with English property names
            $result = New-Object PSObject -Property @{
                'Period' = $period
                'UserLevel' = $level
                'TotalUsers' = $totalUsers
                'ConvertedUsers' = $convertedUsers
                'TotalAmount' = $totalAmount
                'ConversionRate' = $conversionRate
                'AvgOrderValue' = $avgOrderValue
                'AvgPerUser' = $avgPerUser
            }
            
            $results += $result
            
            Write-Host "$level : TotalUsers=$totalUsers, ConvertedUsers=$convertedUsers, TotalAmount=$totalAmount, ConversionRate=$conversionRate%"
        }
    }
}

Write-Host "`n=== Complete Analysis Results ==="

# Display results in specified order
$results | Select-Object 'Period', 'UserLevel', 'TotalUsers', 'ConvertedUsers', 'TotalAmount', 'ConversionRate', 'AvgOrderValue', 'AvgPerUser' | Format-Table -AutoSize

# Save results to CSV file (more compatible)
$results | Select-Object 'Period', 'UserLevel', 'TotalUsers', 'ConvertedUsers', 'TotalAmount', 'ConversionRate', 'AvgOrderValue', 'AvgPerUser' | Export-Csv -Path "user_analysis_results.csv" -NoTypeInformation -Encoding UTF8

Write-Host "`nResults saved to 'user_analysis_results.csv'"

# Try to save to Excel
$results | Select-Object 'Period', 'UserLevel', 'TotalUsers', 'ConvertedUsers', 'TotalAmount', 'ConversionRate', 'AvgOrderValue', 'AvgPerUser' | Export-Excel -Path "user_analysis_results.xlsx" -AutoSize -TableStyle Medium2

Write-Host "Results also saved to 'user_analysis_results.xlsx'"

# Create summary by period
Write-Host "`n=== Summary by Period ==="
$periodSummary = @()

foreach ($period in $periods) {
    $periodResults = $results | Where-Object { $_.Period -eq $period }
    
    $totalUsers = ($periodResults | Measure-Object TotalUsers -Sum).Sum
    $totalConverted = ($periodResults | Measure-Object ConvertedUsers -Sum).Sum
    $totalAmount = ($periodResults | Measure-Object TotalAmount -Sum).Sum
    
    $totalConversionRate = 0
    if ($totalUsers -gt 0) {
        $totalConversionRate = [math]::Round(($totalConverted / $totalUsers) * 100, 2)
    }
    
    $totalAvgOrderValue = 0
    if ($totalConverted -gt 0) {
        $totalAvgOrderValue = [math]::Round($totalAmount / $totalConverted, 2)
    }
    
    $totalAvgPerUser = 0
    if ($totalUsers -gt 0) {
        $totalAvgPerUser = [math]::Round($totalAmount / $totalUsers, 2)
    }
    
    $summary = New-Object PSObject -Property @{
        'Period' = $period
        'TotalUsers' = $totalUsers
        'ConvertedUsers' = $totalConverted
        'TotalAmount' = $totalAmount
        'ConversionRate' = $totalConversionRate
        'AvgOrderValue' = $totalAvgOrderValue
        'AvgPerUser' = $totalAvgPerUser
    }
    
    $periodSummary += $summary
}

$periodSummary | Select-Object 'Period', 'TotalUsers', 'ConvertedUsers', 'TotalAmount', 'ConversionRate', 'AvgOrderValue', 'AvgPerUser' | Format-Table -AutoSize

Write-Host "`nAnalysis completed!"
Read-Host "Press any key to exit"
