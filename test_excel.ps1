# Simple test script to read Excel file

Write-Host "Testing Excel file reading..."

# Import ImportExcel module
Import-Module ImportExcel

# Read Excel file
$excelFile = "用户信息表.xlsx"

if (Test-Path $excelFile) {
    Write-Host "File found: $excelFile"
    
    # Read the data
    $data = Import-Excel -Path $excelFile
    
    Write-Host "Data loaded successfully. Total rows: $($data.Count)"
    
    if ($data.Count -gt 0) {
        # Show column names
        $columns = $data[0].PSObject.Properties.Name
        Write-Host "Columns found:"
        foreach ($col in $columns) {
            Write-Host "  - $col"
        }
        
        # Show first few rows
        Write-Host "`nFirst 3 rows:"
        for ($i = 0; $i -lt [Math]::Min(3, $data.Count); $i++) {
            Write-Host "Row $($i+1):"
            foreach ($col in $columns) {
                $value = $data[$i].$col
                Write-Host "  $col : $value"
            }
            Write-Host ""
        }
        
        # Check for required columns
        $requiredCols = @('期数', '用户等级', '总金额')
        Write-Host "Checking for required columns:"
        foreach ($reqCol in $requiredCols) {
            if ($reqCol -in $columns) {
                Write-Host "  ✓ $reqCol - Found"
            } else {
                Write-Host "  ✗ $reqCol - Missing"
            }
        }
        
        # Get unique values for key columns
        if ('期数' -in $columns) {
            $periods = $data | Select-Object -ExpandProperty '期数' | Where-Object { $_ -ne $null -and $_ -ne '' } | Sort-Object -Unique
            Write-Host "`nUnique periods found: $($periods -join ', ')"
        }
        
        if ('用户等级' -in $columns) {
            $levels = $data | Select-Object -ExpandProperty '用户等级' | Where-Object { $_ -ne $null -and $_ -ne '' } | Sort-Object -Unique
            Write-Host "Unique user levels found: $($levels -join ', ')"
        }
        
    } else {
        Write-Host "No data found in the Excel file."
    }
    
} else {
    Write-Host "Error: File not found - $excelFile"
}

Write-Host "`nTest completed."
