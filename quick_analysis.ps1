# 快速分析脚本

Write-Host "=== 快速用户数据分析 ==="

# 创建Excel COM对象
$excel = New-Object -ComObject Excel.Application
$excel.Visible = $false
$excel.DisplayAlerts = $false

# 获取Excel文件
$excelFile = Get-ChildItem *.xlsx | Select-Object -First 1
$filePath = $excelFile.FullName
Write-Host "读取文件: $($excelFile.Name)"

# 打开工作簿
$workbook = $excel.Workbooks.Open($filePath)
$worksheet = $workbook.Worksheets.Item(1)

# 获取数据范围
$usedRange = $worksheet.UsedRange
$rowCount = $usedRange.Rows.Count
$colCount = $usedRange.Columns.Count

Write-Host "数据行数: $rowCount, 列数: $colCount"

# 创建结果数组
$analysisResults = @()

# 手动分析数据（基于观察到的数据结构）
# 期数在第1列，用户等级在第13列，总金额在第14列

Write-Host "开始分析数据..."

# 统计各期数据
$periods = @("第24期", "第28期", "第32期", "第34期", "第39期", "第45期")
$userLevels = @("SVIP", "高意向", "低意向")

foreach ($period in $periods) {
    Write-Host "`n分析 $period :"
    
    foreach ($level in $userLevels) {
        $totalUsers = 0
        $convertedUsers = 0
        $totalAmount = 0
        
        # 遍历所有数据行
        for ($row = 2; $row -le $rowCount; $row++) {
            $periodValue = $worksheet.Cells.Item($row, 1).Text
            $levelValue = $worksheet.Cells.Item($row, 13).Text
            $amountText = $worksheet.Cells.Item($row, 14).Text
            
            if ($periodValue -eq $period -and $levelValue -eq $level) {
                $totalUsers++
                
                # 转换金额
                $amount = 0
                if ($amountText -ne $null -and $amountText -ne '' -and [double]::TryParse($amountText, [ref]$amount)) {
                    $totalAmount += $amount
                    if ($amount -gt 500) {
                        $convertedUsers++
                    }
                }
            }
        }
        
        if ($totalUsers -gt 0) {
            # 计算指标
            $conversionRate = [math]::Round(($convertedUsers / $totalUsers) * 100, 2)
            $avgOrderValue = if ($convertedUsers -gt 0) { [math]::Round($totalAmount / $convertedUsers, 2) } else { 0 }
            $avgPerUser = [math]::Round($totalAmount / $totalUsers, 2)
            
            $result = [PSCustomObject]@{
                '期数' = $period
                '用户等级' = $level
                '总人数' = $totalUsers
                '转化人数' = $convertedUsers
                '总金额' = $totalAmount
                '转化率(%)' = $conversionRate
                '客单价' = $avgOrderValue
                '人均值' = $avgPerUser
            }
            
            $analysisResults += $result
            
            Write-Host "  $level : 总人数=$totalUsers, 转化人数=$convertedUsers, 总金额=$totalAmount, 转化率=$conversionRate%"
        }
    }
}

# 关闭Excel
$workbook.Close($false)
$excel.Quit()
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null

# 显示结果
Write-Host "`n=== 分析结果 ==="
$analysisResults | Format-Table -AutoSize

# 保存结果
$analysisResults | Export-Csv -Path "分析结果.csv" -NoTypeInformation -Encoding UTF8
Write-Host "`n结果已保存到 '分析结果.csv'"

# 按期数汇总
Write-Host "`n=== 按期数汇总 ==="
$periodSummary = @()

foreach ($period in $periods) {
    $periodData = $analysisResults | Where-Object { $_.'期数' -eq $period }
    
    if ($periodData.Count -gt 0) {
        $totalUsers = ($periodData | Measure-Object '总人数' -Sum).Sum
        $totalConverted = ($periodData | Measure-Object '转化人数' -Sum).Sum
        $totalAmount = ($periodData | Measure-Object '总金额' -Sum).Sum
        
        $totalConversionRate = if ($totalUsers -gt 0) { [math]::Round(($totalConverted / $totalUsers) * 100, 2) } else { 0 }
        $totalAvgOrderValue = if ($totalConverted -gt 0) { [math]::Round($totalAmount / $totalConverted, 2) } else { 0 }
        $totalAvgPerUser = if ($totalUsers -gt 0) { [math]::Round($totalAmount / $totalUsers, 2) } else { 0 }
        
        $summary = [PSCustomObject]@{
            '期数' = $period
            '总人数' = $totalUsers
            '转化人数' = $totalConverted
            '总金额' = $totalAmount
            '转化率(%)' = $totalConversionRate
            '客单价' = $totalAvgOrderValue
            '人均值' = $totalAvgPerUser
        }
        
        $periodSummary += $summary
    }
}

$periodSummary | Format-Table -AutoSize

# 保存汇总结果
$periodSummary | Export-Csv -Path "期数汇总.csv" -NoTypeInformation -Encoding UTF8
Write-Host "期数汇总已保存到 '期数汇总.csv'"

Write-Host "`n=== 分析完成 ==="
