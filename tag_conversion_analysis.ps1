# 结构化标签转化率分析

Write-Host "Starting structured tag conversion analysis..."

# Create Excel COM object
$excel = New-Object -ComObject Excel.Application
$excel.Visible = $false
$excel.DisplayAlerts = $false

# Get Excel file
$excelFile = Get-ChildItem *.xlsx | Select-Object -First 1
$filePath = $excelFile.FullName
Write-Host "Reading file: $($excelFile.Name)"

# Open workbook
$workbook = $excel.Workbooks.Open($filePath)
$worksheet = $workbook.Worksheets.Item(1)

# Get data range
$usedRange = $worksheet.UsedRange
$rowCount = $usedRange.Rows.Count

Write-Host "Total rows: $rowCount"

# Read all data first
$allData = @()
for ($row = 2; $row -le $rowCount; $row++) {
    $period = $worksheet.Cells.Item($row, 1).Text
    $workStage = $worksheet.Cells.Item($row, 2).Text
    $workStatus = $worksheet.Cells.Item($row, 3).Text
    $energyScore = $worksheet.Cells.Item($row, 4).Text
    $cityType = $worksheet.Cells.Item($row, 7).Text
    $workYears = $worksheet.Cells.Item($row, 8).Text
    $salary = $worksheet.Cells.Item($row, 9).Text
    $investment = $worksheet.Cells.Item($row, 10).Text
    $amountText = $worksheet.Cells.Item($row, 14).Text
    
    # Convert amount
    $amount = 0
    if ($amountText -ne $null -and $amountText -ne '' -and [double]::TryParse($amountText, [ref]$amount)) {
        # Amount converted successfully
    }
    
    $dataRow = [PSCustomObject]@{
        Period = $period
        WorkStage = $workStage
        WorkStatus = $workStatus
        EnergyScore = $energyScore
        CityType = $cityType
        WorkYears = $workYears
        Salary = $salary
        Investment = $investment
        Amount = $amount
        IsConverted = ($amount -gt 500)
    }
    
    $allData += $dataRow
}

# Close Excel
$workbook.Close($false)
$excel.Quit()
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null

Write-Host "Data loaded: $($allData.Count) records"

# Get unique periods
$periods = $allData | Where-Object { $_.Period -ne $null -and $_.Period -ne '' } | Select-Object -ExpandProperty Period | Sort-Object -Unique
Write-Host "Periods found: $($periods -join ', ')"

# Analysis function
function Analyze-Tag {
    param($data, $period, $tagProperty, $tagName)
    
    $periodData = $data | Where-Object { $_.Period -eq $period }
    $tags = $periodData | Where-Object { $_.$tagProperty -ne $null -and $_.$tagProperty -ne '' } | Select-Object -ExpandProperty $tagProperty | Sort-Object -Unique
    
    $results = @()
    foreach ($tag in $tags) {
        $tagData = $periodData | Where-Object { $_.$tagProperty -eq $tag }
        
        if ($tagData.Count -gt 0) {
            $totalUsers = $tagData.Count
            $convertedUsers = ($tagData | Where-Object { $_.IsConverted }).Count
            $totalAmount = ($tagData | Measure-Object Amount -Sum).Sum
            
            $conversionRate = if ($totalUsers -gt 0) { [math]::Round(($convertedUsers / $totalUsers) * 100, 2) } else { 0 }
            $avgOrderValue = if ($convertedUsers -gt 0) { [math]::Round($totalAmount / $convertedUsers, 2) } else { 0 }
            $avgPerUser = if ($totalUsers -gt 0) { [math]::Round($totalAmount / $totalUsers, 2) } else { 0 }
            
            $result = [PSCustomObject]@{
                Period = $period
                TagCategory = $tagName
                TagValue = $tag
                TotalUsers = $totalUsers
                ConvertedUsers = $convertedUsers
                TotalAmount = $totalAmount
                ConversionRate = $conversionRate
                AvgOrderValue = $avgOrderValue
                AvgPerUser = $avgPerUser
            }
            
            $results += $result
        }
    }
    
    return $results
}

# Analyze all periods and tags
$allResults = @()

foreach ($period in $periods) {
    Write-Host "Analyzing $period..."
    
    # Work Stage
    $workStageResults = Analyze-Tag -data $allData -period $period -tagProperty "WorkStage" -tagName "WorkStage"
    $allResults += $workStageResults
    
    # Work Status
    $workStatusResults = Analyze-Tag -data $allData -period $period -tagProperty "WorkStatus" -tagName "WorkStatus"
    $allResults += $workStatusResults
    
    # Energy Score
    $energyScoreResults = Analyze-Tag -data $allData -period $period -tagProperty "EnergyScore" -tagName "EnergyScore"
    $allResults += $energyScoreResults
    
    # City Type
    $cityTypeResults = Analyze-Tag -data $allData -period $period -tagProperty "CityType" -tagName "CityType"
    $allResults += $cityTypeResults
    
    # Work Years
    $workYearsResults = Analyze-Tag -data $allData -period $period -tagProperty "WorkYears" -tagName "WorkYears"
    $allResults += $workYearsResults
    
    # Salary
    $salaryResults = Analyze-Tag -data $allData -period $period -tagProperty "Salary" -tagName "Salary"
    $allResults += $salaryResults
    
    # Investment
    $investmentResults = Analyze-Tag -data $allData -period $period -tagProperty "Investment" -tagName "Investment"
    $allResults += $investmentResults
}

# Save all results
$allResults | Export-Csv -Path "AllPeriods_TagAnalysis.csv" -NoTypeInformation -Encoding UTF8
Write-Host "All results saved to 'AllPeriods_TagAnalysis.csv'"

# Show top conversion rates
Write-Host "`nTop 20 Conversion Rates (min 5 users):"
$topResults = $allResults | Where-Object { $_.TotalUsers -ge 5 } | Sort-Object ConversionRate -Descending | Select-Object -First 20
$topResults | Format-Table Period, TagCategory, TagValue, TotalUsers, ConvertedUsers, ConversionRate, AvgOrderValue -AutoSize

# Show Period 24 detailed results
Write-Host "`nPeriod 24 Detailed Results:"
$period24Results = $allResults | Where-Object { $_.Period -eq "第24期" } | Sort-Object ConversionRate -Descending
$period24Results | Format-Table TagCategory, TagValue, TotalUsers, ConvertedUsers, ConversionRate, AvgOrderValue -AutoSize

Write-Host "Analysis complete!"
