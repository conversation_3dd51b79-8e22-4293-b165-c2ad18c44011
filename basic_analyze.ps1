# 基础PowerShell脚本：分析用户数据

Write-Host "开始分析用户数据..."

# 检查ImportExcel模块
$moduleInstalled = Get-Module -ListAvailable -Name ImportExcel
if (-not $moduleInstalled) {
    Write-Host "正在安装ImportExcel模块..."
    Install-Module -Name ImportExcel -Force -Scope CurrentUser -AllowClobber
}

# 导入模块
Import-Module ImportExcel

# 检查文件是否存在
$excelFile = "用户信息表.xlsx"
if (-not (Test-Path $excelFile)) {
    Write-Host "错误：找不到文件 $excelFile"
    Read-Host "按任意键退出"
    exit
}

Write-Host "正在读取Excel文件: $excelFile"

# 读取Excel数据
$data = Import-Excel -Path $excelFile

Write-Host "成功读取数据，共 $($data.Count) 行"

# 显示列名
if ($data.Count -gt 0) {
    $columns = $data[0].PSObject.Properties.Name
    Write-Host "列名: $($columns -join ', ')"
    
    # 显示前3行数据作为示例
    Write-Host "`n前3行数据示例:"
    $data | Select-Object -First 3 | Format-Table
}

# 获取唯一值
$periods = $data | Select-Object -ExpandProperty '期数' | Where-Object { $_ -ne $null -and $_ -ne '' } | Sort-Object -Unique
$userLevels = $data | Select-Object -ExpandProperty '用户等级' | Where-Object { $_ -ne $null -and $_ -ne '' } | Sort-Object -Unique

Write-Host "`n发现的期数: $($periods -join ', ')"
Write-Host "发现的用户等级: $($userLevels -join ', ')"

# 创建结果数组
$results = @()

Write-Host "`n开始详细分析..."

# 分析每个期数的每个用户等级
foreach ($period in $periods) {
    Write-Host "`n--- 分析第 $period 期 ---"
    
    $periodData = $data | Where-Object { $_.'期数' -eq $period }
    
    foreach ($level in $userLevels) {
        $levelData = $periodData | Where-Object { $_.'用户等级' -eq $level }
        
        if ($levelData.Count -gt 0) {
            $totalUsers = $levelData.Count
            $totalAmount = 0
            $convertedUsers = 0
            
            # 逐个处理用户数据
            foreach ($user in $levelData) {
                $amount = 0
                if ($user.'总金额' -ne $null -and $user.'总金额' -ne '') {
                    # 尝试转换为数字
                    if ([double]::TryParse($user.'总金额', [ref]$amount)) {
                        $totalAmount += $amount
                        if ($amount -gt 500) {
                            $convertedUsers++
                        }
                    }
                }
            }
            
            # 计算各项指标
            $conversionRate = 0
            if ($totalUsers -gt 0) {
                $conversionRate = [math]::Round(($convertedUsers / $totalUsers) * 100, 2)
            }
            
            $avgOrderValue = 0
            if ($convertedUsers -gt 0) {
                $avgOrderValue = [math]::Round($totalAmount / $convertedUsers, 2)
            }
            
            $avgPerUser = 0
            if ($totalUsers -gt 0) {
                $avgPerUser = [math]::Round($totalAmount / $totalUsers, 2)
            }
            
            # 创建结果对象
            $result = New-Object PSObject -Property @{
                '期数' = $period
                '用户等级' = $level
                '总人数' = $totalUsers
                '转化人数' = $convertedUsers
                '总金额' = $totalAmount
                '转化率(%)' = $conversionRate
                '客单价' = $avgOrderValue
                '人均值' = $avgPerUser
            }
            
            $results += $result
            
            Write-Host "$level : 总人数=$totalUsers, 转化人数=$convertedUsers, 总金额=$totalAmount, 转化率=$conversionRate%"
        }
    }
}

Write-Host "`n=== 完整分析结果 ==="

# 按指定顺序显示列
$results | Select-Object '期数', '用户等级', '总人数', '转化人数', '总金额', '转化率(%)', '客单价', '人均值' | Format-Table -AutoSize

# 保存结果到CSV文件（更兼容）
$results | Select-Object '期数', '用户等级', '总人数', '转化人数', '总金额', '转化率(%)', '客单价', '人均值' | Export-Csv -Path "用户分析结果.csv" -NoTypeInformation -Encoding UTF8

Write-Host "`n结果已保存到 '用户分析结果.csv'"

# 尝试保存到Excel
$results | Select-Object '期数', '用户等级', '总人数', '转化人数', '总金额', '转化率(%)', '客单价', '人均值' | Export-Excel -Path "用户分析结果.xlsx" -AutoSize -TableStyle Medium2

Write-Host "结果也已保存到 '用户分析结果.xlsx'"

Write-Host "`n分析完成！"
Read-Host "按任意键退出"
